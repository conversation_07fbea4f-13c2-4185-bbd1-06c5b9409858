# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-08 11:52:19

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250808_113639.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 958 条
- **vLLM 平均 e2e 时间**: 12.318s
- **Vidur 平均 e2e 时间**: 7.849s
- **整体平均差异**: -4.468s (-36.3%)

### 关键发现
- **平均绝对相对差异**: 36.2%
- **相对差异标准差**: 5.7%
- **差异范围**: -86.4% 到 -15.5%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 24 条 | 2.5% | 一般 |
| ≥ 20% | 933 条 | 97.5% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 37.6%
- **P75**: 38.8%
- **P90**: 40.5%
- **P95**: 42.2%
- **P99**: 46.9%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 36.2%）
2. **差异稳定**: 标准差 5.7%，说明偏差相对稳定
3. **影响范围**: 97.5% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 36.2% 超出理想范围。

---
*报告生成时间: 2025-08-08 11:52:19*
