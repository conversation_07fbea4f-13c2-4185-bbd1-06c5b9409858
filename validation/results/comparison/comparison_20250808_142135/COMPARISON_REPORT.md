# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-08 14:21:37

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250808_141800.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 908 条
- **vLLM 平均 e2e 时间**: 6.688s
- **Vidur 平均 e2e 时间**: 5.096s
- **整体平均差异**: -1.592s (-23.8%)

### 关键发现
- **平均绝对相对差异**: 24.3%
- **相对差异标准差**: 3.2%
- **差异范围**: -56.6% 到 -12.6%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 65 条 | 7.3% | 一般 |
| ≥ 20% | 842 条 | 92.7% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 24.3%
- **P75**: 25.3%
- **P90**: 27.2%
- **P95**: 29.0%
- **P99**: 34.1%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 24.3%）
2. **差异稳定**: 标准差 3.2%，说明偏差相对稳定
3. **影响范围**: 92.7% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 24.3% 超出理想范围。

---
*报告生成时间: 2025-08-08 14:21:37*
