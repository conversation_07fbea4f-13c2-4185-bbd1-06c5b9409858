# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-11 10:43:51

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250811_103911.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 994 条
- **vLLM 平均 e2e 时间**: 6.606s
- **Vidur 平均 e2e 时间**: 5.533s
- **整体平均差异**: -1.073s (-16.2%)

### 关键发现
- **平均绝对相对差异**: 17.2%
- **相对差异标准差**: 4.2%
- **差异范围**: -58.2% 到 -8.0%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 4 条 | 0.4% | 良好 |
| 10-20% | 923 条 | 92.9% | 一般 |
| ≥ 20% | 66 条 | 6.7% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 16.4%
- **P75**: 17.0%
- **P90**: 18.8%
- **P95**: 21.6%
- **P99**: 40.1%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 17.2%）
2. **差异稳定**: 标准差 4.2%，说明偏差相对稳定
3. **影响范围**: 6.7% 的请求差异超过 20%

## 💡 **结论**

👍 **Vidur 显示出可接受的保真度**，平均差异 17.2%，有改进空间。

---
*报告生成时间: 2025-08-11 10:43:51*
