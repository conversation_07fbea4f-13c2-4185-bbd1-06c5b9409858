# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-08 16:35:33

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250808_163343.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 6.537s
- **Vidur 平均 e2e 时间**: 5.502s
- **整体平均差异**: -1.035s (-15.8%)

### 关键发现
- **平均绝对相对差异**: 16.9%
- **相对差异标准差**: 4.8%
- **差异范围**: -62.0% 到 +10.0%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 2 条 | 0.2% | 优秀 |
| 5-10% | 2 条 | 0.2% | 良好 |
| 10-20% | 933 条 | 93.8% | 一般 |
| ≥ 20% | 58 条 | 5.8% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 16.1%
- **P75**: 16.7%
- **P90**: 18.3%
- **P95**: 21.5%
- **P99**: 43.7%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 16.9%）
2. **差异稳定**: 标准差 4.8%，说明偏差相对稳定
3. **影响范围**: 5.8% 的请求差异超过 20%

## 💡 **结论**

👍 **Vidur 显示出可接受的保真度**，平均差异 16.9%，有改进空间。

---
*报告生成时间: 2025-08-08 16:35:33*
