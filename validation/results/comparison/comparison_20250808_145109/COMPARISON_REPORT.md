# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-08 14:51:11

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250808_144923.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 8.788s
- **Vidur 平均 e2e 时间**: 6.781s
- **整体平均差异**: -2.007s (-22.8%)

### 关键发现
- **平均绝对相对差异**: 23.9%
- **相对差异标准差**: 5.4%
- **差异范围**: -69.1% 到 -13.4%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 110 条 | 11.1% | 一般 |
| ≥ 20% | 885 条 | 88.9% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 22.6%
- **P75**: 25.2%
- **P90**: 29.4%
- **P95**: 31.6%
- **P99**: 46.4%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 23.9%）
2. **差异稳定**: 标准差 5.4%，说明偏差相对稳定
3. **影响范围**: 88.9% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 23.9% 超出理想范围。

---
*报告生成时间: 2025-08-08 14:51:11*
