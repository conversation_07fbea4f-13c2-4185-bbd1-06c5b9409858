# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-08 14:41:20

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250808_143425.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 954 条
- **vLLM 平均 e2e 时间**: 8.898s
- **Vidur 平均 e2e 时间**: 6.819s
- **整体平均差异**: -2.079s (-23.4%)

### 关键发现
- **平均绝对相对差异**: 23.6%
- **相对差异标准差**: 4.2%
- **差异范围**: -49.8% 到 -14.6%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 157 条 | 16.5% | 一般 |
| ≥ 20% | 797 条 | 83.5% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 22.7%
- **P75**: 26.2%
- **P90**: 29.5%
- **P95**: 30.9%
- **P99**: 36.4%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 23.6%）
2. **差异稳定**: 标准差 4.2%，说明偏差相对稳定
3. **影响范围**: 83.5% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 23.6% 超出理想范围。

---
*报告生成时间: 2025-08-08 14:41:20*
