# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-08 16:16:22

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250808_161355.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 39.321s
- **Vidur 平均 e2e 时间**: 18.740s
- **整体平均差异**: -20.581s (-52.3%)

### 关键发现
- **平均绝对相对差异**: 50.2%
- **相对差异标准差**: 18.5%
- **差异范围**: -99.8% 到 -12.5%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 78 条 | 7.8% | 一般 |
| ≥ 20% | 917 条 | 92.2% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 51.8%
- **P75**: 65.6%
- **P90**: 71.7%
- **P95**: 73.6%
- **P99**: 92.0%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 50.2%）
2. **差异稳定**: 标准差 18.5%，说明偏差相对稳定
3. **影响范围**: 92.2% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 50.2% 超出理想范围。

---
*报告生成时间: 2025-08-08 16:16:22*
